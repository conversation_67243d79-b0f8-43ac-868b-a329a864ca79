package domain

// LoanType represents the hrm_loan_types table structure
type LoanType struct {
	LoanTypeID   int64   `json:"loan_type_id" db:"types_id"`
	LoanTypeName string  `json:"loan_type_name" db:"name"`
	Principal    float64 `json:"principal" db:"principal"`
	InterestRate float64 `json:"interest_rate" db:"interest_rate"`
	TermMonths   int     `json:"term_months" db:"term_months"`
	AdminFkid    int     `json:"admin_fkid" db:"admin_fkid"`
	CreatedAt    int64   `json:"created_at" db:"created_at"`
	UpdatedAt    int64   `json:"updated_at" db:"updated_at"`
}

// Loan represents the hrm_loans table structure
type Loan struct {
	LoanID           int64   `json:"loan_id" db:"loan_id"`
	HrmEmployeeFkid  int     `json:"hrm_employee_fkid" db:"hrm_employee_fkid"`
	LoanTypeFkid     int64   `json:"loan_type_fkid" db:"loan_type_fkid"`
	Principal        float64 `json:"principal" db:"principal"`
	InterestRate     float64 `json:"interest_rate" db:"interest_rate"`
	TermMonths       int     `json:"term_months" db:"term_months"`
	FirstDueDate     string  `json:"first_due_date" db:"first_due_date"`
	TotalDue         float64 `json:"total_due" db:"total_due"`
	OutstandingTotal float64 `json:"outstanding_total" db:"outstanding_total"`
	Notes            string  `json:"notes" db:"notes"`
	Status           string  `json:"status" db:"status"`
	CreatedAt        int64   `json:"created_at" db:"created_at"`
	UpdatedAt        int64   `json:"updated_at" db:"updated_at"`
}

// LoanRequest represents the request payload for creating a new loan
type LoanRequest struct {
	HrmEmployeeFkid int     `json:"hrm_employee_fkid" validate:"required" message:"required:Employee ID is required"`
	LoanTypeName    string  `json:"loan_type_name" validate:"required" message:"required:Loan type name is required"`
	Principal       float64 `json:"principal" validate:"required|gt:0" message:"required:Principal is required|gt:Principal must be greater than 0"`
	InterestRate    float64 `json:"interest_rate" validate:"gte:0" message:"gte:Interest rate cannot be negative"`
	TermMonths      int     `json:"term_months" validate:"required|gt:0" message:"required:Term months is required|gt:Term months must be greater than 0"`
	FirstDueDate    string  `json:"first_due_date" validate:"required|date|future_date" message:"required:First due date is required|date:First due date must be in YYYY-MM-DD format|future_date:First due date cannot be in the past"`
	Notes           string  `json:"notes"`
}

// LoanDetail represents loan data with joined information
type LoanDetail struct {
	LoanID           int64   `json:"loan_id" db:"loan_id"`
	HrmEmployeeFkid  int     `json:"hrm_employee_fkid" db:"hrm_employee_fkid"`
	EmployeeName     string  `json:"employee_name" db:"employee_name"`
	EmployeeNIK      string  `json:"employee_nik" db:"employee_nik"`
	LoanTypeFkid     int64   `json:"loan_type_fkid" db:"loan_type_fkid"`
	LoanTypeName     string  `json:"loan_type_name" db:"loan_type_name"`
	Principal        float64 `json:"principal" db:"principal"`
	InterestRate     float64 `json:"interest_rate" db:"interest_rate"`
	TermMonths       int     `json:"term_months" db:"term_months"`
	FirstDueDate     string  `json:"first_due_date" db:"first_due_date"`
	TotalDue         float64 `json:"total_due" db:"total_due"`
	OutstandingTotal float64 `json:"outstanding_total" db:"outstanding_total"`
	Notes            string  `json:"notes" db:"notes"`
	Status           string  `json:"status" db:"status"`
	CreatedAt        int64   `json:"created_at" db:"created_at"`
	UpdatedAt        int64   `json:"updated_at" db:"updated_at"`
}

// LoanContract interface defines the basic operations for loans
type LoanContract interface {
	FetchLoanTypes(adminFkid int) ([]LoanType, error)
	FetchLoans(adminFkid int) ([]LoanDetail, error)
	AddLoan(loan LoanRequest, adminFkid int) (int64, error)
}

// LoanUseCase interface defines the business logic operations
type LoanUseCase interface {
	LoanContract
}

// LoanRepository interface defines the data access operations
type LoanRepository interface {
	LoanContract
	FindOrCreateLoanType(loan LoanRequest, adminFkid int) (int64, error)
}
