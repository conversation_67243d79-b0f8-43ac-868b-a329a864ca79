package usecase

import (
	"fmt"
	"time"

	"github.com/gookit/validate"
	"gitlab.com/backend/api-hrm/domain"
)

type loanUseCase struct {
	loanRepository domain.LoanRepository
}

// NewLoanUseCase creates a new loan use case
func NewLoanUseCase(lr domain.LoanRepository) domain.LoanUseCase {
	// Register custom validator for future dates
	validate.AddValidator("future_date", validateFutureDate)
	return &loanUseCase{loanRepository: lr}
}

// validateFutureDate validates that a date string is not in the past
func validateFutureDate(val interface{}) bool {
	dateStr, ok := val.(string)
	if !ok {
		return false
	}

	// Parse the date
	dueDate, err := time.Parse("2006-01-02", dateStr)
	if err != nil {
		return false
	}

	// Get current date (without time component for comparison)
	now := time.Now()
	today := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())

	// Check if due date is not in the past
	return !dueDate.Before(today)
}

// FetchLoanTypes retrieves all loan types for the given admin
func (l *loanUseCase) FetchLoanTypes(adminFkid int) ([]domain.LoanType, error) {
	return l.loanRepository.FetchLoanTypes(adminFkid)
}

// FetchLoans retrieves all loans with employee and loan type information for the given admin
func (l *loanUseCase) FetchLoans(adminFkid int) ([]domain.LoanDetail, error) {
	return l.loanRepository.FetchLoans(adminFkid)
}

// AddLoan creates a new loan with business logic validation
func (l *loanUseCase) AddLoan(loan domain.LoanRequest, adminFkid int) (int64, error) {
	// Use gookit/validate for struct validation
	data, err := validate.FromStruct(loan)
	if err != nil {
		return 0, fmt.Errorf("error creating validator: %v", err)
	}

	v := data.Create()
	if !v.Validate() {
		return 0, domain.ValidationException{Message: v.Errors.Error(), ValidatinFieldsErr: v.Errors.All()}
	}

	// Call repository to create the loan
	return l.loanRepository.AddLoan(loan, adminFkid)
}
